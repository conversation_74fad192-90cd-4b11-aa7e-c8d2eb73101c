{"name": "trade-journal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.21.3", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "papaparse": "^5.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}